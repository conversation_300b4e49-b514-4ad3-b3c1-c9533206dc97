"use client";

import React from "react";
import { motion } from "motion/react";
import Logo from "./Logo";

export default function Navbar() {
  return (
    <header className="w-full border-b border-border bg-background/60 backdrop-blur-sm">
      <nav
        className="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between"
        aria-label="Main Navigation"
      >
        <div className="flex items-center gap-6">
          <Logo />
          <ul
            className="hidden md:flex items-center gap-4 text-sm"
            role="menubar"
            aria-label="Primary"
          >
            <li>
              <a className="hover:underline" href="#">
                Home
              </a>
            </li>
            <li>
              <a className="hover:underline" href="#about">
                About Us
              </a>
            </li>
            <li>
              <a className="hover:underline" href="#contact">
                Contact
              </a>
            </li>
            <li>
              <details className="relative">
                <summary className="list-none cursor-pointer select-none">
                  Services
                </summary>
                <ul className="absolute mt-2 w-56 bg-card text-card-foreground rounded-md shadow-lg p-2">
                  <li>
                    <a
                      className="block px-3 py-2 rounded hover:bg-muted/60"
                      href="#"
                    >
                      Datera: data & research
                    </a>
                  </li>
                  <li>
                    <a
                      className="block px-3 py-2 rounded hover:bg-muted/60"
                      href="#"
                    >
                      Pison Learning
                    </a>
                  </li>
                  <li>
                    <a
                      className="block px-3 py-2 rounded hover:bg-muted/60"
                      href="#"
                    >
                      Enowise Publishing
                    </a>
                  </li>
                </ul>
              </details>
            </li>
            <li>
              <a className="hover:underline" href="#products">
                Products
              </a>
            </li>
            <li>
              <a className="hover:underline" href="#blog">
                Blog
              </a>
            </li>
            <li>
              <a className="hover:underline" href="#social">
                Social Media
              </a>
            </li>
          </ul>
        </div>

        <div className="flex items-center gap-4">
          <a
            className="hidden md:inline-block rounded-full px-4 py-2 bg-yellow-400 text-black font-medium"
            href="#signup"
            aria-label="Sign up for Pison"
          >
            Signup
          </a>

          {/* Mobile hamburger */}
          <motion.button
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.35 }}
            className="md:hidden p-2 rounded focus:outline-none"
            aria-label="Open menu"
          >
            <svg
              width="24"
              height="24"
              fill="none"
              viewBox="0 0 24 24"
              aria-hidden
            >
              <path
                d="M4 6h16M4 12h16M4 18h16"
                stroke="currentColor"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </motion.button>
        </div>
      </nav>
    </header>
  );
}
