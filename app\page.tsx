import Navbar from "../components/Navbar";
import Hero from "../components/Hero";
import Services from "../components/Services";
import Products from "../components/Products";
import CoreValues from "../components/CoreValues";
import Footer from "../components/Footer";

export default function Home() {
  return (
    <div className="min-h-screen font-sans">
      <Navbar />
      <main>
        <Hero />

        <section id="about" className="max-w-6xl mx-auto px-6 py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <h2 className="text-2xl font-semibold">About Us</h2>
              <p className="mt-3 text-muted-foreground">
                <PERSON><PERSON> brings together education, research and publishing to make
                knowledge accessible to learners, institutions and researchers
                worldwide. We deliver tailored training, reliable data and
                trusted publishing services.
              </p>

              <p className="mt-4 text-muted-foreground">
                Placeholder: our team combines domain expertise with research
                experience to design programs, perform rigorous analysis and
                publish high-quality outputs.
              </p>
            </div>

            <div className="flex items-center justify-center">
              <img
                src="/about-illustration.svg"
                alt="Education illustration"
                className="w-full max-w-sm rounded-lg shadow"
              />
            </div>
          </div>
        </section>

        <Services />
        <Products />
        <CoreValues />
      </main>
      <Footer />
    </div>
  );
}
