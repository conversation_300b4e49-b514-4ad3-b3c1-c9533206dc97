"use client";

import React from "react";
import { motion } from "motion/react";

export default function Hero() {
  return (
    <section className="relative overflow-hidden">
      <div
        className="absolute inset-0 bg-gradient-to-br from-green-50 via-transparent to-transparent opacity-40 pointer-events-none"
        aria-hidden
      />
      <div className="max-w-6xl mx-auto px-6 py-20 md:py-32 flex flex-col md:flex-row items-center gap-12">
        <motion.div
          initial={{ opacity: 0, x: -16 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          className="flex-1"
        >
          <h1 className="text-4xl md:text-5xl font-extrabold leading-tight">
            Welcome to Pison
          </h1>
          <p className="mt-4 text-muted-foreground max-w-xl">
            Promoting knowledge, creating information for growth
          </p>
          <p className="mt-4 text-lg">We simplify learning and research.</p>

          <div className="mt-8 flex gap-3">
            <motion.a
              href="#get-started"
              initial={{ opacity: 0, y: 8 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.15 }}
              className="inline-flex items-center rounded-md px-5 py-3 bg-green-700 text-white font-semibold"
              aria-label="Get started with Pison"
            >
              Get Started
            </motion.a>

            <motion.a
              href="#learn-more"
              initial={{ opacity: 0, y: 8 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.25 }}
              className="inline-flex items-center rounded-md px-5 py-3 bg-yellow-400 text-black font-semibold"
              aria-label="Learn more about Pison"
            >
              Learn More
            </motion.a>
          </div>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, scale: 0.98 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.35 }}
          className="flex-1 flex items-center justify-center"
          aria-hidden
        >
          <div className="w-64 h-64 rounded-xl bg-white/80 shadow-lg flex items-center justify-center">
            <svg
              width="120"
              height="120"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 2v6"
                stroke="#166534"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M6 8v10a2 2 0 002 2h8a2 2 0 002-2V8"
                stroke="#f59e0b"
                strokeWidth="1.2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M8 12h8"
                stroke="#166534"
                strokeWidth="1.2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
