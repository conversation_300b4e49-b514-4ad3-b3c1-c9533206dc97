"use client";

import React from "react";
import { motion } from "motion/react";

const services = [
  {
    title: "Datera",
    subtitle: "Data & research services",
    href: "/services/datera",
  },
  {
    title: "Pison Learning",
    subtitle: "Training and global education",
    href: "/services/pison-learning",
  },
  {
    title: "Enowise Publishing",
    subtitle:
      "Publishing of journals, books, book series, and conference proceedings",
    href: "/services/enowise-publishing",
  },
];

export default function Services() {
  return (
    <section id="services" className="max-w-6xl mx-auto px-6 py-16">
      <h2 className="text-3xl font-semibold">Our Services</h2>
      <p className="mt-3 text-muted-foreground max-w-2xl">
        We deliver focused research, training, and publishing services to help
        organizations and learners grow.
      </p>

      <div className="mt-8 grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {services.map((s) => (
          <motion.a
            key={s.title}
            href={s.href}
            className="block rounded-xl p-6 bg-card hover:shadow-xl transition-shadow border border-transparent hover:border-border"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.995 }}
            aria-label={`${s.title} — ${s.subtitle}`}
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-foreground">
                  {s.title}
                </h3>
                <p className="mt-2 text-muted-foreground text-sm">
                  {s.subtitle}
                </p>
              </div>

              <div className="ml-4 flex items-center">
                <span className="w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-black font-semibold">
                  →
                </span>
              </div>
            </div>
          </motion.a>
        ))}
      </div>
    </section>
  );
}
