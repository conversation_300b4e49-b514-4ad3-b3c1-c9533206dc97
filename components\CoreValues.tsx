"use client";

import React from "react";
import { motion } from "motion/react";

const values = [
  "Professionalism",
  "Integrity",
  "Value for money",
  "Quality service delivery",
  "Collaboration",
];

export default function CoreValues() {
  return (
    <section className="max-w-6xl mx-auto px-6 py-12">
      <h2 className="text-3xl font-semibold">Core Values</h2>
      <p className="mt-3 text-muted-foreground max-w-2xl">
        Our work is guided by a set of core values that ensure quality and
        trustworthiness in everything we deliver.
      </p>

      <div className="mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4">
        {values.map((v, i) => (
          <motion.div
            key={v}
            className="rounded-lg p-4 bg-card border border-transparent hover:border-border"
            initial={{ opacity: 0, y: 8 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: i * 0.06 }}
          >
            <h3 className="font-semibold text-foreground">{v}</h3>
            <p className="mt-2 text-sm text-muted-foreground">Placeholder description for {v.toLowerCase()} demonstrating how we apply this value.</p>
          </motion.div>
        ))}
      </div>
    </section>
  );
}
